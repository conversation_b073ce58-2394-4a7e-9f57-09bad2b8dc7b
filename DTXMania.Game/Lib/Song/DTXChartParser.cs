using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DTX.Song.Components;

namespace DTX.Song
{
    /// <summary>
    /// DTX chart parser for gameplay note data
    /// Parses DTX files to extract notes for the 9 NX lanes (channels 11-19)
    /// Based on DTXManiaNX parsing patterns
    /// </summary>
    public class DTXChartParser
    {
        #region Constants

        /// <summary>
        /// DTX channel to lane index mapping
        /// Channels 11-19 map to lanes 0-8
        /// </summary>
        private static readonly Dictionary<int, int> ChannelToLaneMap = new Dictionary<int, int>
        {
            { 0x11, 0 }, // LC - Left Cymbal
            { 0x12, 1 }, // LP - Left Pedal  
            { 0x13, 2 }, // HH - Hi-Hat
            { 0x14, 3 }, // SD - Snare Drum
            { 0x15, 4 }, // BD - <PERSON>
            { 0x16, 5 }, // HT - High Tom
            { 0x17, 6 }, // LT - <PERSON>
            { 0x18, 7 }, // FT - Floor <PERSON>
            { 0x19, 8 }  // CY - Cymbal
        };

        /// <summary>
        /// Ticks per measure in DTX format
        /// </summary>
        private const int TicksPerMeasure = 192;

        /// <summary>
        /// Default BPM if not specified
        /// </summary>
        private const double DefaultBpm = 120.0;

        #endregion

        #region Public Methods

        /// <summary>
        /// Parses a DTX file and returns a ParsedChart with notes and metadata
        /// </summary>
        /// <param name="filePath">Path to the DTX file</param>
        /// <returns>ParsedChart containing notes and metadata</returns>
        public static async Task<ParsedChart> ParseAsync(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                throw new FileNotFoundException($"DTX file not found: {filePath}");

            var chart = new ParsedChart(filePath);
            var wavDefinitions = new Dictionary<string, string>(); // WAV ID -> file path

            // Try different encodings for Japanese text support
            var encodings = new List<Encoding>
            {
                Encoding.UTF8,
                Encoding.Default
            };

            // Try to add Shift_JIS if available
            try
            {
                encodings.Add(Encoding.GetEncoding("Shift_JIS"));
            }
            catch (ArgumentException)
            {
                // Shift_JIS not available, continue with available encodings
            }

            Exception lastException = null;

            foreach (var encoding in encodings)
            {
                try
                {
                    using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                    using var reader = new StreamReader(stream, encoding);

                    await ParseFileContentAsync(reader, chart, wavDefinitions);
                    
                    // If we got here without exception, parsing succeeded
                    break;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    // Try next encoding
                    continue;
                }
            }

            // If all encodings failed, throw the last exception
            if (chart.Notes.Count == 0 && lastException != null)
            {
                throw new InvalidOperationException($"Failed to parse DTX file with any encoding: {filePath}", lastException);
            }

            // Find background audio file
            FindBackgroundAudio(chart, wavDefinitions, filePath);

            // Finalize the chart
            chart.Finalize();

            return chart;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Parses the content of a DTX file
        /// </summary>
        private static async Task ParseFileContentAsync(StreamReader reader, ParsedChart chart, Dictionary<string, string> wavDefinitions)
        {
            string line;
            bool inDataSection = false;

            while ((line = await reader.ReadLineAsync()) != null)
            {
                line = line.Trim();

                // Skip empty lines and comments
                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("//"))
                    continue;

                // Check if we've reached the data section
                if (line.StartsWith("*") || line.StartsWith("[") || IsTimelineData(line))
                {
                    inDataSection = true;
                }

                if (!inDataSection)
                {
                    // Parse header commands
                    ParseHeaderCommand(line, chart, wavDefinitions);
                }
                else
                {
                    // Parse measure data
                    ParseMeasureData(line, chart);
                }
            }
        }

        /// <summary>
        /// Parses header commands like #BPM and #WAV definitions
        /// </summary>
        private static void ParseHeaderCommand(string line, ParsedChart chart, Dictionary<string, string> wavDefinitions)
        {
            if (!line.StartsWith("#"))
                return;

            var colonIndex = line.IndexOf(':');
            if (colonIndex == -1)
                return;

            var command = line.Substring(0, colonIndex).Trim().ToUpperInvariant();
            var value = line.Substring(colonIndex + 1).Trim();

            // Remove quotes if present
            if (value.StartsWith("\"") && value.EndsWith("\"") && value.Length > 1)
            {
                value = value.Substring(1, value.Length - 2);
            }

            switch (command)
            {
                case "#BPM":
                    if (TryParseDouble(value, out var bpm))
                        chart.Bpm = bpm;
                    break;

                default:
                    // Check for WAV definitions: #WAV01, #WAV02, etc.
                    if (command.StartsWith("#WAV") && command.Length > 4)
                    {
                        var wavId = command.Substring(4);
                        wavDefinitions[wavId] = value;
                    }
                    break;
            }
        }

        /// <summary>
        /// Parses measure data lines for note information
        /// </summary>
        private static void ParseMeasureData(string line, ParsedChart chart)
        {
            if (!IsTimelineData(line) || !line.Contains(":"))
                return;

            var colonIndex = line.IndexOf(':');
            var measureChannelPart = line.Substring(1, colonIndex - 1); // Remove # or *
            var noteData = line.Substring(colonIndex + 1).Trim();

            // Parse measure and channel
            if (!TryParseMeasureAndChannel(measureChannelPart, out int measure, out int channel))
                return;

            // Check if this is a drum lane channel (11-19)
            if (!ChannelToLaneMap.ContainsKey(channel))
                return;

            var laneIndex = ChannelToLaneMap[channel];

            // Parse notes from the data
            ParseNotesFromData(noteData, measure, channel, laneIndex, chart);
        }

        /// <summary>
        /// Parses individual notes from measure data
        /// </summary>
        private static void ParseNotesFromData(string noteData, int measure, int channel, int laneIndex, ParsedChart chart)
        {
            if (string.IsNullOrWhiteSpace(noteData))
                return;

            // DTX uses pairs of characters to represent notes
            // Each pair represents one note position within the measure
            var pairCount = noteData.Length / 2;
            if (pairCount == 0)
                return;

            for (int i = 0; i < pairCount; i++)
            {
                if (i * 2 + 1 >= noteData.Length)
                    break;

                var pair = noteData.Substring(i * 2, 2);

                // Skip empty notes (00)
                if (pair == "00" || string.IsNullOrWhiteSpace(pair))
                    continue;

                // Calculate tick position within the measure
                var tick = (int)((double)i / pairCount * TicksPerMeasure);

                // Create note
                var note = new Note(laneIndex, measure, tick, channel, pair);
                chart.AddNote(note);
            }
        }

        /// <summary>
        /// Checks if a line contains timeline data (measure data)
        /// </summary>
        private static bool IsTimelineData(string line)
        {
            if (line.StartsWith("#") && line.Length >= 6)
            {
                var measurePart = line.Substring(1, 5);
                return measurePart.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f'));
            }
            return false;
        }

        /// <summary>
        /// Tries to parse measure and channel from measure data line
        /// </summary>
        private static bool TryParseMeasureAndChannel(string measureChannelPart, out int measure, out int channel)
        {
            measure = 0;
            channel = 0;

            if (measureChannelPart.Length != 5) // MMMCC format
                return false;

            var measureStr = measureChannelPart.Substring(0, 3); // First 3 chars = measure
            var channelStr = measureChannelPart.Substring(3, 2); // Last 2 chars = channel

            return int.TryParse(measureStr, out measure) &&
                   int.TryParse(channelStr, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out channel);
        }

        /// <summary>
        /// Finds the background audio file from WAV definitions
        /// </summary>
        private static void FindBackgroundAudio(ParsedChart chart, Dictionary<string, string> wavDefinitions, string dtxFilePath)
        {
            // Find the first WAV file referenced in the definitions
            // This is typically the main background music
            var firstWav = wavDefinitions.Values.FirstOrDefault();
            if (!string.IsNullOrEmpty(firstWav))
            {
                // Make path relative to DTX file location
                var dtxDirectory = Path.GetDirectoryName(dtxFilePath) ?? "";
                chart.BackgroundAudioPath = Path.Combine(dtxDirectory, firstWav);
            }
        }

        /// <summary>
        /// Safely parses a double value
        /// </summary>
        private static bool TryParseDouble(string value, out double result)
        {
            result = 0.0;
            if (string.IsNullOrEmpty(value))
                return false;

            return double.TryParse(value, NumberStyles.Float, CultureInfo.InvariantCulture, out result);
        }

        #endregion
    }
}
